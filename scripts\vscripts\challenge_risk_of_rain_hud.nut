FONT_DEFAULT <- self.LookupFont( "Default" );
FONT_DEFAULTLARGE <- self.LookupFont( "DefaultLarge" );
FONT_DEFAULTEXTRALARGE <- self.LookupFont( "DefaultExtraLarge" );

function Paint()
{
	if ( self.GetEntity(0) != GetLocalPlayer().GetViewNPC() )
	{
		return;
	}
	if (self.GetInt(0) > 0)
	{
		self.PaintRectangleFade
		(
			ScreenPosX( 0.0600 ),
			ScreenPosY( 0.0650 ) - ScreenPosX( 0.0025 ),
			ScreenPosX( 0.0750 ) + self.GetTextWide(FONT_DEFAULTLARGE, "Marine Level: Lv. " + self.GetInt(2).tostring()),
			ScreenPosY( 0.0700 ) + ScreenPosX( 0.0025 ) + self.GetFontTall(FONT_DEFAULTLARGE),
			0, 0, 0, 0, 125,
			ScreenPosX( 0.0600 ),
			ScreenPosX( 0.0750 ) + self.GetTextWide(FONT_DEFAULTLARGE, "Marine Level: Lv. " + self.GetInt(2).tostring()),
			true
		);
		self.PaintRectangleFade
		(
			ScreenPosX( 0.0625 ),
			ScreenPosY( 0.0650 ),
			ScreenPosX( 0.0725 ) + self.GetTextWide(FONT_DEFAULTLARGE, "Marine Level: Lv. " + self.GetInt(2).tostring()),
			ScreenPosY( 0.0700 ) + self.GetFontTall(FONT_DEFAULTLARGE),
			0, 255, 0, 0, 10,
			ScreenPosX( 0.0625 ),
			ScreenPosX( 0.0725 ) + self.GetTextWide(FONT_DEFAULTLARGE, "Marine Level: Lv. " + self.GetInt(2).tostring()),
			true
		);
		self.PaintText
		(
			ScreenPosX( 0.0675 ),
			ScreenPosY( 0.0675 ),
			255, 255, 255, 255,
			FONT_DEFAULTLARGE,
			"Marine Level: Lv. " + self.GetInt(2).tostring()
		);
		local stageTextWidth = self.GetTextWide(FONT_DEFAULT, "Stage " + self.GetInt(0).tostring());
		if (self.GetTextWide(FONT_DEFAULT, "Lv. " + self.GetInt(1).tostring()) > self.GetTextWide(FONT_DEFAULT, "Stage " + self.GetInt(0).tostring()))
		{
			stageTextWidth = self.GetTextWide(FONT_DEFAULT, "Lv. " + self.GetInt(1).tostring());
		}
		self.PaintRectangle
		(
			ScreenPosX( 0.9000 ) - self.GetTextWide(FONT_DEFAULTEXTRALARGE, split(self.GetString(0), "|")[1]) - self.GetTextWide(FONT_DEFAULTLARGE, split(self.GetString(0), "|")[0].toupper()) - stageTextWidth,
			ScreenPosY( 0.0650 ) - ScreenPosX( 0.0025 ),
			ScreenPosX( 0.9400 ),
			ScreenPosY( 0.0700 ) + ScreenPosX( 0.0025 ) + self.GetFontTall(FONT_DEFAULTEXTRALARGE),
			0, 0, 0, 125
		);
		self.PaintRectangleFade
		(
			ScreenPosX( 0.9025 ) - self.GetTextWide(FONT_DEFAULTEXTRALARGE, split(self.GetString(0), "|")[1]) - self.GetTextWide(FONT_DEFAULTLARGE, split(self.GetString(0), "|")[0].toupper()) - stageTextWidth,
			ScreenPosY( 0.0650 ),
			ScreenPosX( 0.9125 ) - self.GetTextWide(FONT_DEFAULTEXTRALARGE, split(self.GetString(0), "|")[1]) - self.GetTextWide(FONT_DEFAULTLARGE, split(self.GetString(0), "|")[0].toupper()),
			ScreenPosY( 0.0700 ) + self.GetFontTall(FONT_DEFAULTEXTRALARGE),
			0, 255, 255, 55, 5,
			ScreenPosY( 0.0650 ),
			ScreenPosY( 0.0700 ) + self.GetFontTall(FONT_DEFAULTEXTRALARGE),
			false
		);
		self.PaintRectangleFade
		(
			ScreenPosX( 0.9150 ) - self.GetTextWide(FONT_DEFAULTEXTRALARGE, split(self.GetString(0), "|")[1]) - self.GetTextWide(FONT_DEFAULTLARGE, split(self.GetString(0), "|")[0].toupper()),
			ScreenPosY( 0.0650 ),
			ScreenPosX( 0.9250 ) - self.GetTextWide(FONT_DEFAULTEXTRALARGE, split(self.GetString(0), "|")[1]),
			ScreenPosY( 0.0700 ) + self.GetFontTall(FONT_DEFAULTEXTRALARGE),
			255, 255, 0, 55, 5,
			ScreenPosY( 0.0650 ),
			ScreenPosY( 0.0700 ) + self.GetFontTall(FONT_DEFAULTEXTRALARGE),
			false
		);
		self.PaintRectangleFade
		(
			ScreenPosX( 0.9275 ) - self.GetTextWide(FONT_DEFAULTEXTRALARGE, split(self.GetString(0), "|")[1]),
			ScreenPosY( 0.0650 ),
			ScreenPosX( 0.9375 ),
			ScreenPosY( 0.0700 ) + self.GetFontTall(FONT_DEFAULTEXTRALARGE),
			255, 255, 255, 55, 5,
			ScreenPosY( 0.0650 ),
			ScreenPosY( 0.0700 ) + self.GetFontTall(FONT_DEFAULTEXTRALARGE),
			false
		);
		self.PaintText
		(
			ScreenPosX( 0.9075 ) - self.GetTextWide(FONT_DEFAULTEXTRALARGE, split(self.GetString(0), "|")[1]) - self.GetTextWide(FONT_DEFAULTLARGE, split(self.GetString(0), "|")[0].toupper()) - self.GetTextWide(FONT_DEFAULT, "Stage " + self.GetInt(0).tostring()),
			ScreenPosY( 0.0675 ) + (self.GetFontTall(FONT_DEFAULTEXTRALARGE)-self.GetFontTall(FONT_DEFAULT)*2)/3,
			255, 255, 255, 255,
			FONT_DEFAULT,
			"Stage " + self.GetInt(0).tostring()
		);
		self.PaintText
		(
			ScreenPosX( 0.9075 ) - self.GetTextWide(FONT_DEFAULTEXTRALARGE, split(self.GetString(0), "|")[1]) - self.GetTextWide(FONT_DEFAULTLARGE, split(self.GetString(0), "|")[0].toupper()) - self.GetTextWide(FONT_DEFAULT, "Lv. " + self.GetInt(1).tostring()),
			ScreenPosY( 0.0675 ) + (self.GetFontTall(FONT_DEFAULTEXTRALARGE)-self.GetFontTall(FONT_DEFAULT)*2)/3*2 + self.GetFontTall(FONT_DEFAULT),
			255, 255, 255, 255,
			FONT_DEFAULT,
			"Lv. " + self.GetInt(1).tostring()
		);
		self.PaintText
		(
			ScreenPosX( 0.9200 ) - self.GetTextWide(FONT_DEFAULTEXTRALARGE, split(self.GetString(0), "|")[1]) - self.GetTextWide(FONT_DEFAULTLARGE, split(self.GetString(0), "|")[0].toupper()),
			ScreenPosY( 0.0675 ) + (self.GetFontTall(FONT_DEFAULTEXTRALARGE)-self.GetFontTall(FONT_DEFAULTLARGE))/2,
			255, 255, 255, 255,
			FONT_DEFAULTLARGE,
			split(self.GetString(0), "|")[0].toupper()
		);
		self.PaintText
		(
			ScreenPosX( 0.9325 ) - self.GetTextWide(FONT_DEFAULTEXTRALARGE, split(self.GetString(0), "|")[1]),
			ScreenPosY( 0.0675 ),
			255, 255, 255, 255,
			FONT_DEFAULTEXTRALARGE,
			split(self.GetString(0), "|")[1]
		);
	}
	if (self.GetInt(3) > 0)
	{
		self.PaintRectangle
		(
			ScreenPosX( 0.2500 ),
			ScreenPosY( 0.1500 ),
			ScreenPosX( 0.7500 ),
			ScreenPosY( 0.1650 ),
			0, 0, 0, 125
		);
		self.PaintRectangleFade
		(
			ScreenPosX( 0.2250 ),
			ScreenPosY( 0.1500 ),
			ScreenPosX( 0.2500 ),
			ScreenPosY( 0.1650 ),
			0, 0, 0, 0, 125,
			ScreenPosX( 0.2250 ),
			ScreenPosX( 0.2500 ),
			true
		);
		self.PaintRectangleFade
		(
			ScreenPosX( 0.7500 ),
			ScreenPosY( 0.1500 ),
			ScreenPosX( 0.7750 ),
			ScreenPosY( 0.1650 ),
			0, 0, 0, 175, 0,
			ScreenPosX( 0.7500 ),
			ScreenPosX( 0.7750 ),
			true
		);
		self.PaintRectangle
		(
			ScreenPosX( 0.2525 ),
			ScreenPosY( 0.1525 ),
			ScreenPosX( 0.2525 + 0.4950*(self.GetInt(3).tofloat()/self.GetInt(4).tofloat()) ),
			ScreenPosY( 0.1625 ),
			103, 0, 0, 255
		);
		self.PaintText
		(
			ScreenPosX( 0.5000 ) - self.GetTextWide(FONT_DEFAULT, self.GetInt(3).tostring() + "/" + self.GetInt(4).tostring())/2,
			ScreenPosY( 0.1525 ) + (ScreenPosY( 0.01 ) - self.GetFontTall(FONT_DEFAULT))/2,
			255, 255, 255, 255,
			FONT_DEFAULT,
			self.GetInt(3).tostring() + "/" + self.GetInt(4).tostring()
		);
		self.PaintText
		(
			ScreenPosX( 0.5000 ) - self.GetTextWide(FONT_DEFAULTEXTRALARGE, "Telos")/2,
			ScreenPosY( 0.1650 ),
			255, 255, 255, 255,
			FONT_DEFAULTEXTRALARGE,
			"Telos"
		);
		self.PaintText
		(
			ScreenPosX( 0.5000 ) - self.GetTextWide(FONT_DEFAULT, "The Final Stop of Your Journey")/2,
			ScreenPosY( 0.1675 ) + self.GetFontTall(FONT_DEFAULTEXTRALARGE),
			255, 255, 255, 255,
			FONT_DEFAULT,
			"The Final Stop of Your Journey"
		);
	}
}

function ScreenPosX( fFraction )
{
	return ScreenWidth() * fFraction;
}
function ScreenPosY( fFraction )
{
	return ScreenHeight() * fFraction;
}